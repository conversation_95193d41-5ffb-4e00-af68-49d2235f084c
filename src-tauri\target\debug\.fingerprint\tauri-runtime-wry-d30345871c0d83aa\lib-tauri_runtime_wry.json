{"rustc": 1842507548689473721, "features": "[\"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 10956804577811572050, "deps": [[4381063397040571828, "webview2_com", false, 2570650744882149664], [7653476968652377684, "windows", false, 3715085020098981444], [8292277814562636972, "tauri_utils", false, 3002225434926668147], [8319709847752024821, "uuid", false, 6408279202360987943], [8391357152270261188, "wry", false, 9952784790831982184], [11693073011723388840, "raw_window_handle", false, 2214521491364938754], [13208667028893622512, "rand", false, 14866442077337734985], [14162324460024849578, "tauri_runtime", false, 5177127885870400966], [16228250612241359704, "build_script_build", false, 1384881318742936544]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-d30345871c0d83aa\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}