{"rustc": 1842507548689473721, "features": "[\"std\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 6355579909791343455, "path": 1021026050783098276, "deps": [[1906322745568073236, "pin_project_lite", false, 16371255440629486796], [3424551429995674438, "tracing_core", false, 13125834372426315773]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-791e79c9dbf8844c\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}