import React, { useState } from "react";
import { Globe, Lock, Refresh, ArrowLeft, ArrowRight, Home, Star, MoreHorizontal } from "lucide-react";

export function BrowserView() {
    const [url, setUrl] = useState("https://");
    const [isLoading, setIsLoading] = useState(false);

    const handleUrlSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        // Simulate loading
        setTimeout(() => {
            setIsLoading(false);
        }, 1000);
    };

    const handleNavigation = (direction: 'back' | 'forward' | 'refresh' | 'home') => {
        setIsLoading(true);
        setTimeout(() => setIsLoading(false), 500);
    };

    return (
        <div className="flex-1 flex flex-col bg-gray-50">
            {/* Browser Navigation Bar */}
            <div className="bg-white border-b border-gray-200 px-4 py-2">
                <div className="flex items-center gap-3">
                    {/* Navigation Buttons */}
                    <div className="flex items-center gap-1">
                        <button
                            onClick={() => handleNavigation('back')}
                            className="p-2 rounded-lg hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors"
                            title="Go back"
                        >
                            <ArrowLeft className="w-5 h-5" />
                        </button>
                        <button
                            onClick={() => handleNavigation('forward')}
                            className="p-2 rounded-lg hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors"
                            title="Go forward"
                        >
                            <ArrowRight className="w-5 h-5" />
                        </button>
                        <button
                            onClick={() => handleNavigation('refresh')}
                            className={`p-2 rounded-lg hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors ${isLoading ? 'animate-spin' : ''}`}
                            title="Refresh"
                        >
                            <Refresh className="w-5 h-5" />
                        </button>
                        <button
                            onClick={() => handleNavigation('home')}
                            className="p-2 rounded-lg hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors"
                            title="Home"
                        >
                            <Home className="w-5 h-5" />
                        </button>
                    </div>

                    {/* URL Address Bar */}
                    <form onSubmit={handleUrlSubmit} className="flex-1 max-w-4xl">
                        <div className="relative">
                            <div className="flex items-center bg-gray-100 rounded-lg border border-gray-300 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-200">
                                <div className="flex items-center pl-3">
                                    <Lock className="w-4 h-4 text-green-600" />
                                    <Globe className="w-4 h-4 text-gray-500 ml-1" />
                                </div>
                                <input
                                    type="text"
                                    value={url}
                                    onChange={(e) => setUrl(e.target.value)}
                                    className="flex-1 px-3 py-3 bg-transparent text-gray-800 placeholder-gray-500 focus:outline-none text-base"
                                    placeholder="Enter URL or search..."
                                    autoFocus
                                />
                                <button
                                    type="submit"
                                    className="px-4 py-2 text-blue-600 hover:text-blue-800 font-medium"
                                >
                                    Go
                                </button>
                            </div>
                        </div>
                    </form>

                    {/* Action Buttons */}
                    <div className="flex items-center gap-1">
                        <button className="p-2 rounded-lg hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors">
                            <Star className="w-5 h-5" />
                        </button>
                        <button className="p-2 rounded-lg hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors">
                            <MoreHorizontal className="w-5 h-5" />
                        </button>
                    </div>
                </div>
            </div>

            {/* Browser Content Area */}
            <div className="flex-1 bg-white">
                {isLoading ? (
                    <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p className="text-gray-600">Loading...</p>
                        </div>
                    </div>
                ) : (
                    <div className="flex items-center justify-center h-full">
                        <div className="text-center max-w-md">
                            <div className="mb-6">
                                <Globe className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                                <h1 className="text-3xl font-bold text-gray-800 mb-2">Welcome to Memori Browser</h1>
                                <p className="text-gray-600 mb-6">AI-Enhanced Browsing Experience</p>
                            </div>

                            <div className="space-y-3">
                                <button
                                    onClick={() => setUrl("https://google.com")}
                                    className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    Visit Google
                                </button>
                                <button
                                    onClick={() => setUrl("https://github.com")}
                                    className="w-full px-4 py-3 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors"
                                >
                                    Visit GitHub
                                </button>
                                <button
                                    onClick={() => setUrl("https://stackoverflow.com")}
                                    className="w-full px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                                >
                                    Visit Stack Overflow
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
