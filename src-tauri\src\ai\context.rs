use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

use super::AIAnalysis;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BrowsingContext {
    pub current_url: String,
    pub current_title: String,
    pub session_id: String,
    pub time_spent: i64,
    pub scroll_depth: f32,
    pub interactions: Vec<UserInteraction>,
    pub related_pages: Vec<String>,
    pub topics: Vec<String>,
    pub intent: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UserInteraction {
    pub interaction_type: String,
    pub element: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub data: HashMap<String, String>,
}

pub struct ContextManager {
    current_context: BrowsingContext,
    context_history: Vec<BrowsingContext>,
}

impl ContextManager {
    pub async fn new() -> Result<Self> {
        Ok(Self {
            current_context: BrowsingContext {
                current_url: String::new(),
                current_title: String::new(),
                session_id: uuid::Uuid::new_v4().to_string(),
                time_spent: 0,
                scroll_depth: 0.0,
                interactions: Vec::new(),
                related_pages: Vec::new(),
                topics: Vec::new(),
                intent: None,
            },
            context_history: Vec::new(),
        })
    }

    pub async fn update_with_analysis(&mut self, analysis: &AIAnalysis) -> Result<()> {
        self.current_context.current_url = analysis.url.clone();
        self.current_context.current_title = analysis.title.clone();
        self.current_context.topics = analysis.topics.clone();
        
        // Infer intent from analysis
        if analysis.topics.contains(&"shopping".to_string()) {
            self.current_context.intent = Some("shopping".to_string());
        } else if analysis.topics.contains(&"research".to_string()) {
            self.current_context.intent = Some("research".to_string());
        }
        
        Ok(())
    }

    pub async fn get_current_context(&self) -> Result<String> {
        Ok(serde_json::to_string(&self.current_context)?)
    }

    pub async fn learn_from_interaction(&mut self, interaction_data: &str) -> Result<()> {
        let interaction: UserInteraction = serde_json::from_str(interaction_data)?;
        self.current_context.interactions.push(interaction);
        
        // Update context based on interactions
        self.analyze_interaction_patterns().await?;
        
        Ok(())
    }

    async fn analyze_interaction_patterns(&mut self) -> Result<()> {
        // Analyze recent interactions to understand user behavior
        let recent_interactions: Vec<_> = self.current_context.interactions
            .iter()
            .rev()
            .take(10)
            .collect();

        // Look for patterns
        let click_count = recent_interactions.iter()
            .filter(|i| i.interaction_type == "click")
            .count();
            
        let scroll_count = recent_interactions.iter()
            .filter(|i| i.interaction_type == "scroll")
            .count();

        // Adjust intent based on interaction patterns
        if click_count > 5 && scroll_count < 2 {
            self.current_context.intent = Some("focused_reading".to_string());
        } else if scroll_count > 10 {
            self.current_context.intent = Some("browsing".to_string());
        }

        Ok(())
    }
}
