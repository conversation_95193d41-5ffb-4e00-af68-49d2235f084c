{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 18439556900360439042, "deps": [[3060637413840920116, "proc_macro2", false, 14143828410907889523], [4899080583175475170, "semver", false, 11305946926429843468], [7392050791754369441, "ico", false, 1652095514456372069], [8008191657135824715, "thiserror", false, 4520877778235573845], [8292277814562636972, "tauri_utils", false, 16919622050845248091], [8319709847752024821, "uuid", false, 6408279202360987943], [9689903380558560274, "serde", false, 1218481086981197372], [9857275760291862238, "sha2", false, 10548895577560400280], [10301936376833819828, "json_patch", false, 6661969753541700740], [12687914511023397207, "png", false, 17587159174617512687], [14132538657330703225, "brotli", false, 12692731928378737861], [15367738274754116744, "serde_json", false, 18289888665409790972], [15622660310229662834, "walkdir", false, 3350024720424388675], [17990358020177143287, "quote", false, 9791753232960825671], [18066890886671768183, "base64", false, 856503484629315922]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-eb3dbbe25d337782\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}