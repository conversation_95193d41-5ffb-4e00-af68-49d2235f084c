{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 16403927197798629762, "deps": [[561782849581144631, "html5ever", false, 13986292834336833969], [3150220818285335163, "url", false, 16680178351721664748], [3334271191048661305, "windows_version", false, 4088894940548983748], [4071963112282141418, "serde_with", false, 5272260465928985803], [4899080583175475170, "semver", false, 776467486246627426], [5986029879202738730, "log", false, 15103776660954145007], [6262254372177975231, "kuchiki", false, 7170849159953836823], [6606131838865521726, "ctor", false, 7246181784518508507], [6997837210367702832, "infer", false, 6806554301072325548], [8008191657135824715, "thiserror", false, 4520877778235573845], [9689903380558560274, "serde", false, 1218481086981197372], [10301936376833819828, "json_patch", false, 13991835213317207854], [11989259058781683633, "dunce", false, 13399713777212210225], [14132538657330703225, "brotli", false, 12692731928378737861], [15367738274754116744, "serde_json", false, 2855758561636807193], [15622660310229662834, "walkdir", false, 992662056628988890], [15932120279885307830, "memchr", false, 8878295398709515274], [17155886227862585100, "glob", false, 16951528391663687881], [17186037756130803222, "phf", false, 14379691183729926014]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-bbc1c3e8ae2d96d1\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}