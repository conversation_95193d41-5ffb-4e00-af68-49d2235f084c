{"rustc": 1842507548689473721, "features": "[\"compression\", \"default\", \"objc-exception\", \"tauri-runtime-wry\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 5144538945173598434, "path": 2762511390010927866, "deps": [[40386456601120721, "percent_encoding", false, 14044020114199803101], [1260461579271933187, "serialize_to_javascript", false, 15031491424525688888], [1441306149310335789, "tempfile", false, 13624526677259464386], [3150220818285335163, "url", false, 16137435904022318695], [3722963349756955755, "once_cell", false, 6551543648015782790], [4381063397040571828, "webview2_com", false, 16200934873096540251], [4405182208873388884, "http", false, 5183562168063699956], [4450062412064442726, "dirs_next", false, 4624430257488765046], [4899080583175475170, "semver", false, 776467486246627426], [5180608563399064494, "tauri_macros", false, 1105349260226003341], [5610773616282026064, "build_script_build", false, 78632535042165300], [5986029879202738730, "log", false, 15103776660954145007], [7653476968652377684, "windows", false, 3715085020098981444], [8008191657135824715, "thiserror", false, 4520877778235573845], [8292277814562636972, "tauri_utils", false, 3002225434926668147], [8319709847752024821, "uuid", false, 6408279202360987943], [9623796893764309825, "ignore", false, 8619721222791980057], [9689903380558560274, "serde", false, 1218481086981197372], [9920160576179037441, "getrandom", false, 3395342022260403445], [10629569228670356391, "futures_util", false, 9146900771434437838], [11601763207901161556, "tar", false, 6374689842049079696], [11693073011723388840, "raw_window_handle", false, 2214521491364938754], [11989259058781683633, "dunce", false, 13399713777212210225], [12393800526703971956, "tokio", false, 1125559654807146096], [12986574360607194341, "serde_repr", false, 5969797084531658756], [13208667028893622512, "rand", false, 14866442077337734985], [13625485746686963219, "anyhow", false, 2147913167118214086], [14162324460024849578, "tauri_runtime", false, 2998002850750930849], [14564311161534545801, "encoding_rs", false, 5633263186074840385], [15367738274754116744, "serde_json", false, 2855758561636807193], [16228250612241359704, "tauri_runtime_wry", false, 16055047004330515015], [17155886227862585100, "glob", false, 16951528391663687881], [17278893514130263345, "state", false, 16206874382359834904], [17772299992546037086, "flate2", false, 1536030757479782467]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-c29d3dc4f3facdd8\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}