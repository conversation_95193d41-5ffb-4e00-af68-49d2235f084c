[package]
name = "memori-browser"
version = "1.0.0"
description = "The lightest, fastest, AI-enhanced browser across all platforms"
authors = ["Memori Team"]
license = "MIT"
repository = "https://github.com/your-username/memori-browser"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
tauri = { version = "2.0", features = ["shell-open"] }
tauri-plugin-shell = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite"] }
reqwest = { version = "0.11", features = ["json"] }
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
thiserror = "1.0"

# AI and ML dependencies
candle-core = "0.3"
candle-nn = "0.3"
candle-transformers = "0.3"
hf-hub = "0.3"
tokenizers = "0.15"

# Vector database and search
qdrant-client = "1.7"
tantivy = "0.21"

# Additional dependencies
url = "2.4"
bincode = "1.3"

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
