{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 5144538945173598434, "path": 112649233362061231, "deps": [[3150220818285335163, "url", false, 16680178351721664748], [4381063397040571828, "webview2_com", false, 2570650744882149664], [4405182208873388884, "http", false, 9858713576818919528], [7653476968652377684, "windows", false, 3715085020098981444], [8008191657135824715, "thiserror", false, 4520877778235573845], [8292277814562636972, "tauri_utils", false, 5481747727737904185], [8319709847752024821, "uuid", false, 6408279202360987943], [8866577183823226611, "http_range", false, 11421844695356099708], [9689903380558560274, "serde", false, 1218481086981197372], [11693073011723388840, "raw_window_handle", false, 2214521491364938754], [13208667028893622512, "rand", false, 14866442077337734985], [14162324460024849578, "build_script_build", false, 1624035464376540273], [15367738274754116744, "serde_json", false, 2855758561636807193]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-252d91ea0e1b942e\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}