{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 21500578101221346, "deps": [[4450062412064442726, "dirs_next", false, 4624430257488765046], [4899080583175475170, "semver", false, 11305946926429843468], [7468248713591957673, "cargo_toml", false, 453419767593204653], [8292277814562636972, "tauri_utils", false, 8040028731283328363], [9689903380558560274, "serde", false, 1218481086981197372], [10301936376833819828, "json_patch", false, 6661969753541700740], [13077543566650298139, "heck", false, 11003689655865491247], [13625485746686963219, "anyhow", false, 2147913167118214086], [14189313126492979171, "tauri_winres", false, 13944705175972765724], [15367738274754116744, "serde_json", false, 18289888665409790972], [15622660310229662834, "walkdir", false, 4785404358478762107]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-8a43dad4e23bf0ba\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}