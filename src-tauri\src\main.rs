// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::Manager;
use std::sync::Arc;
use tokio::sync::Mutex;

mod memory;
mod ai;
mod browser;
mod search;
mod utils;

use memory::MemoryEngine;
use ai::AIEngine;
use browser::BrowserEngine;
use search::SearchEngine;

// Application state
#[derive(Clone)]
pub struct AppState {
    pub memory: Arc<Mutex<MemoryEngine>>,
    pub ai: Arc<Mutex<AIEngine>>,
    pub browser: Arc<Mutex<BrowserEngine>>,
    pub search: Arc<Mutex<SearchEngine>>,
}

// Tauri commands
#[tauri::command]
async fn navigate_to(url: String, state: tauri::State<'_, AppState>) -> Result<String, String> {
    let mut browser = state.browser.lock().await;
    browser.navigate(&url).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn search_memory(query: String, state: tauri::State<'_, AppState>) -> Result<Vec<String>, String> {
    let memory = state.memory.lock().await;
    memory.search(&query).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn ai_analyze_page(url: String, content: String, state: tauri::State<'_, AppState>) -> Result<String, String> {
    let ai = state.ai.lock().await;
    ai.analyze_page(&url, &content).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_smart_suggestions(context: String, state: tauri::State<'_, AppState>) -> Result<Vec<String>, String> {
    let ai = state.ai.lock().await;
    ai.get_suggestions(&context).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn save_browsing_session(session_data: String, state: tauri::State<'_, AppState>) -> Result<(), String> {
    let mut memory = state.memory.lock().await;
    memory.save_session(&session_data).await.map_err(|e| e.to_string())
}

#[tokio::main]
async fn main() {
    // Initialize engines
    let memory_engine = MemoryEngine::new().await.expect("Failed to initialize memory engine");
    let ai_engine = AIEngine::new().await.expect("Failed to initialize AI engine");
    let browser_engine = BrowserEngine::new().await.expect("Failed to initialize browser engine");
    let search_engine = SearchEngine::new().await.expect("Failed to initialize search engine");

    let app_state = AppState {
        memory: Arc::new(Mutex::new(memory_engine)),
        ai: Arc::new(Mutex::new(ai_engine)),
        browser: Arc::new(Mutex::new(browser_engine)),
        search: Arc::new(Mutex::new(search_engine)),
    };

    tauri::Builder::default()
        .plugin(tauri_plugin_shell::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            navigate_to,
            search_memory,
            ai_analyze_page,
            get_smart_suggestions,
            save_browsing_session
        ])
        .setup(|app| {
            #[cfg(debug_assertions)]
            {
                let window = app.get_webview_window("main").unwrap();
                window.open_devtools();
            }
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
