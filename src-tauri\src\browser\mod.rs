use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Tab {
    pub id: String,
    pub url: String,
    pub title: String,
    pub favicon: Option<String>,
    pub is_active: bool,
    pub is_loading: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_accessed: chrono::DateTime<chrono::Utc>,
    pub group_id: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TabGroup {
    pub id: String,
    pub name: String,
    pub color: String,
    pub tabs: Vec<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NavigationHistory {
    pub entries: Vec<HistoryEntry>,
    pub current_index: usize,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct HistoryEntry {
    pub url: String,
    pub title: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

pub struct BrowserEngine {
    tabs: HashMap<String, Tab>,
    tab_groups: HashMap<String, TabGroup>,
    active_tab_id: Option<String>,
    history: NavigationHistory,
}

impl BrowserEngine {
    pub async fn new() -> Result<Self> {
        Ok(Self {
            tabs: HashMap::new(),
            tab_groups: HashMap::new(),
            active_tab_id: None,
            history: NavigationHistory {
                entries: Vec::new(),
                current_index: 0,
            },
        })
    }

    pub async fn navigate(&mut self, url: &str) -> Result<String> {
        // Create or update the active tab
        let tab_id = if let Some(active_id) = &self.active_tab_id {
            active_id.clone()
        } else {
            self.create_new_tab(url).await?
        };

        // Update tab with new URL
        if let Some(tab) = self.tabs.get_mut(&tab_id) {
            tab.url = url.to_string();
            tab.title = format!("Loading {}", url);
            tab.is_loading = true;
            tab.last_accessed = chrono::Utc::now();
        }

        // Add to history
        self.history.entries.push(HistoryEntry {
            url: url.to_string(),
            title: format!("Loading {}", url),
            timestamp: chrono::Utc::now(),
        });
        self.history.current_index = self.history.entries.len() - 1;

        // Simulate navigation completion
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        
        if let Some(tab) = self.tabs.get_mut(&tab_id) {
            tab.is_loading = false;
            tab.title = self.extract_title_from_url(url);
        }

        Ok(format!("Navigated to {}", url))
    }

    pub async fn create_new_tab(&mut self, url: &str) -> Result<String> {
        let tab_id = Uuid::new_v4().to_string();
        let now = chrono::Utc::now();

        let tab = Tab {
            id: tab_id.clone(),
            url: url.to_string(),
            title: self.extract_title_from_url(url),
            favicon: None,
            is_active: true,
            is_loading: false,
            created_at: now,
            last_accessed: now,
            group_id: None,
        };

        // Deactivate other tabs
        for tab in self.tabs.values_mut() {
            tab.is_active = false;
        }

        self.tabs.insert(tab_id.clone(), tab);
        self.active_tab_id = Some(tab_id.clone());

        Ok(tab_id)
    }

    pub async fn close_tab(&mut self, tab_id: &str) -> Result<()> {
        self.tabs.remove(tab_id);
        
        if self.active_tab_id.as_ref() == Some(&tab_id.to_string()) {
            // Find another tab to activate
            if let Some((new_active_id, _)) = self.tabs.iter().next() {
                self.active_tab_id = Some(new_active_id.clone());
                if let Some(tab) = self.tabs.get_mut(new_active_id) {
                    tab.is_active = true;
                }
            } else {
                self.active_tab_id = None;
            }
        }

        Ok(())
    }

    pub async fn create_tab_group(&mut self, name: &str, tab_ids: Vec<String>) -> Result<String> {
        let group_id = Uuid::new_v4().to_string();
        
        let group = TabGroup {
            id: group_id.clone(),
            name: name.to_string(),
            color: self.generate_group_color(),
            tabs: tab_ids.clone(),
            created_at: chrono::Utc::now(),
        };

        // Update tabs to belong to this group
        for tab_id in &tab_ids {
            if let Some(tab) = self.tabs.get_mut(tab_id) {
                tab.group_id = Some(group_id.clone());
            }
        }

        self.tab_groups.insert(group_id.clone(), group);
        Ok(group_id)
    }

    pub fn get_tabs(&self) -> Vec<&Tab> {
        self.tabs.values().collect()
    }

    pub fn get_active_tab(&self) -> Option<&Tab> {
        if let Some(active_id) = &self.active_tab_id {
            self.tabs.get(active_id)
        } else {
            None
        }
    }

    fn extract_title_from_url(&self, url: &str) -> String {
        // Simple title extraction from URL
        if let Ok(parsed_url) = url::Url::parse(url) {
            if let Some(host) = parsed_url.host_str() {
                return host.to_string();
            }
        }
        url.to_string()
    }

    fn generate_group_color(&self) -> String {
        let colors = ["blue", "red", "green", "purple", "orange", "pink"];
        let index = self.tab_groups.len() % colors.len();
        colors[index].to_string()
    }
}
