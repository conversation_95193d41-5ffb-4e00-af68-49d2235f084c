use anyhow::Result;
use serde::{Deserialize, Serialize};
use sqlx::{SqlitePool, Row};
use std::collections::HashMap;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryEntry {
    pub id: String,
    pub url: String,
    pub title: String,
    pub content: String,
    pub embeddings: Vec<f32>,
    pub metadata: HashMap<String, String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub access_count: i32,
    pub last_accessed: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowsingSession {
    pub id: String,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    pub pages_visited: Vec<String>,
    pub total_time: i64,
    pub session_type: String,
}

pub struct MemoryEngine {
    db_pool: SqlitePool,
}

impl MemoryEngine {
    pub async fn new() -> Result<Self> {
        let db_pool = SqlitePool::connect("sqlite:memori_memory.db").await?;
        
        // Initialize database tables
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS memory_entries (
                id TEXT PRIMARY KEY,
                url TEXT NOT NULL,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                embeddings BLOB,
                metadata TEXT,
                timestamp DATETIME NOT NULL,
                access_count INTEGER DEFAULT 0,
                last_accessed DATETIME NOT NULL
            )
            "#,
        )
        .execute(&db_pool)
        .await?;

        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS browsing_sessions (
                id TEXT PRIMARY KEY,
                start_time DATETIME NOT NULL,
                end_time DATETIME,
                pages_visited TEXT,
                total_time INTEGER,
                session_type TEXT
            )
            "#,
        )
        .execute(&db_pool)
        .await?;

        Ok(Self { db_pool })
    }

    pub async fn store_page(&mut self, url: &str, title: &str, content: &str, embeddings: Vec<f32>) -> Result<String> {
        let id = Uuid::new_v4().to_string();
        let now = chrono::Utc::now();
        let embeddings_bytes = bincode::serialize(&embeddings)?;
        
        sqlx::query(
            r#"
            INSERT INTO memory_entries (id, url, title, content, embeddings, metadata, timestamp, access_count, last_accessed)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&id)
        .bind(url)
        .bind(title)
        .bind(content)
        .bind(embeddings_bytes)
        .bind("{}")
        .bind(now)
        .bind(0)
        .bind(now)
        .execute(&self.db_pool)
        .await?;

        Ok(id)
    }

    pub async fn search(&self, query: &str) -> Result<Vec<String>> {
        // Simple text search for now
        // In production, this would use vector similarity search
        let rows = sqlx::query(
            r#"
            SELECT id, url, title, content 
            FROM memory_entries 
            WHERE content LIKE ? OR title LIKE ?
            ORDER BY last_accessed DESC
            LIMIT 10
            "#,
        )
        .bind(format!("%{}%", query))
        .bind(format!("%{}%", query))
        .fetch_all(&self.db_pool)
        .await?;

        let mut results = Vec::new();
        for row in rows {
            let entry = MemoryEntry {
                id: row.get("id"),
                url: row.get("url"),
                title: row.get("title"),
                content: row.get("content"),
                embeddings: Vec::new(), // Don't load embeddings for search results
                metadata: HashMap::new(),
                timestamp: chrono::Utc::now(),
                access_count: 0,
                last_accessed: chrono::Utc::now(),
            };
            results.push(serde_json::to_string(&entry)?);
        }

        Ok(results)
    }

    pub async fn save_session(&mut self, session_data: &str) -> Result<()> {
        let session: BrowsingSession = serde_json::from_str(session_data)?;
        
        sqlx::query(
            r#"
            INSERT INTO browsing_sessions (id, start_time, end_time, pages_visited, total_time, session_type)
            VALUES (?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&session.id)
        .bind(session.start_time)
        .bind(session.end_time)
        .bind(serde_json::to_string(&session.pages_visited)?)
        .bind(session.total_time)
        .bind(&session.session_type)
        .execute(&self.db_pool)
        .await?;

        Ok(())
    }
}
