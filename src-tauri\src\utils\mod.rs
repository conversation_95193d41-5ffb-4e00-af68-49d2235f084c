use anyhow::Result;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub startup_time: u64,
    pub memory_usage: u64,
    pub cpu_usage: f32,
    pub tab_switch_time: u64,
    pub ai_response_time: u64,
}

pub struct Utils;

impl Utils {
    pub fn extract_domain(url: &str) -> Result<String> {
        let parsed = url::Url::parse(url)?;
        Ok(parsed.host_str().unwrap_or("unknown").to_string())
    }

    pub fn sanitize_filename(filename: &str) -> String {
        filename
            .chars()
            .map(|c| match c {
                '/' | '\\' | ':' | '*' | '?' | '"' | '<' | '>' | '|' => '_',
                _ => c,
            })
            .collect()
    }

    pub fn format_file_size(bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;

        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }

        format!("{:.1} {}", size, UNITS[unit_index])
    }

    pub fn get_performance_metrics() -> PerformanceMetrics {
        // Simulate performance metrics collection
        PerformanceMetrics {
            startup_time: 300, // ms
            memory_usage: 45 * 1024 * 1024, // 45MB
            cpu_usage: 2.5, // %
            tab_switch_time: 30, // ms
            ai_response_time: 150, // ms
        }
    }

    pub fn is_valid_url(url: &str) -> bool {
        url::Url::parse(url).is_ok()
    }

    pub fn generate_session_id() -> String {
        uuid::Uuid::new_v4().to_string()
    }

    pub fn hash_content(content: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        content.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }
}
