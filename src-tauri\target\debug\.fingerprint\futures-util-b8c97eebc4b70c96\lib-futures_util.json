{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 15389663814530670383, "deps": [[1615478164327904835, "pin_utils", false, 8274102835293351996], [1906322745568073236, "pin_project_lite", false, 16371255440629486796], [5451793922601807560, "slab", false, 11084205599844982025], [7620660491849607393, "futures_core", false, 235090336762385746], [10565019901765856648, "futures_macro", false, 6867220186773163203], [16240732885093539806, "futures_task", false, 6774818387472760842]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-b8c97eebc4b70c96\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}