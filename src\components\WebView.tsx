import React, { useState, useRef, useEffect } from "react";

interface WebViewProps {
    url: string;
    onLoad?: () => void;
    onError?: () => void;
    onTitleChange?: (title: string) => void;
}

export function WebView({ url, onLoad, onError, onTitleChange }: WebViewProps) {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);
    const iframeRef = useRef<HTMLIFrameElement>(null);

    useEffect(() => {
        setIsLoading(true);
        setHasError(false);
    }, [url]);

    const handleLoad = () => {
        setIsLoading(false);
        onLoad?.();
        
        // Try to get the title from the iframe
        try {
            const iframe = iframeRef.current;
            if (iframe?.contentDocument) {
                const title = iframe.contentDocument.title;
                if (title) {
                    onTitleChange?.(title);
                }
            }
        } catch (e) {
            // Cross-origin restrictions prevent access to iframe content
            console.log("Cannot access iframe content due to CORS");
        }
    };

    const handleError = () => {
        setIsLoading(false);
        setHasError(true);
        onError?.();
    };

    if (hasError) {
        return (
            <div style={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "#f8f9fa",
                flexDirection: "column",
                gap: "20px"
            }}>
                <div style={{ fontSize: "48px" }}>⚠️</div>
                <div style={{ textAlign: "center", maxWidth: "400px" }}>
                    <h3 style={{ color: "#dc3545", marginBottom: "12px" }}>
                        Unable to Load Website
                    </h3>
                    <p style={{ color: "#6c757d", marginBottom: "20px" }}>
                        This website cannot be displayed in an embedded frame due to security restrictions.
                    </p>
                    <button
                        onClick={() => window.open(url, '_blank')}
                        style={{
                            padding: "12px 24px",
                            backgroundColor: "#0d6efd",
                            color: "white",
                            border: "none",
                            borderRadius: "6px",
                            cursor: "pointer",
                            fontSize: "14px",
                            fontWeight: "500"
                        }}
                    >
                        Open in System Browser
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div style={{ width: "100%", height: "100%", position: "relative" }}>
            {isLoading && (
                <div style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: "#f8f9fa",
                    zIndex: 1000
                }}>
                    <div style={{ textAlign: "center" }}>
                        <div style={{ 
                            fontSize: "48px", 
                            marginBottom: "16px",
                            animation: "spin 2s linear infinite"
                        }}>
                            ⏳
                        </div>
                        <p style={{ color: "#6c757d", fontSize: "18px" }}>
                            Loading website...
                        </p>
                    </div>
                </div>
            )}
            
            <iframe
                ref={iframeRef}
                src={url}
                style={{
                    width: "100%",
                    height: "100%",
                    border: "none",
                    backgroundColor: "#ffffff"
                }}
                onLoad={handleLoad}
                onError={handleError}
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation allow-downloads"
                allow="accelerometer; autoplay; camera; encrypted-media; geolocation; gyroscope; microphone; midi; payment; usb; vr; xr-spatial-tracking"
                loading="lazy"
                title="Web content"
            />
        </div>
    );
}
