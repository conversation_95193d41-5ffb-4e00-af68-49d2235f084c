﻿import React, { useState } from "react";

function App() {
    const [url, setUrl] = useState("https://");

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        alert(`You entered: ${url}`);
    };

    return (
        <div style={{
            height: "100vh",
            display: "flex",
            flexDirection: "column",
            fontFamily: "Arial, sans-serif",
            backgroundColor: "#f5f5f5"
        }}>
            {/* Header */}
            <div style={{
                backgroundColor: "white",
                padding: "10px",
                borderBottom: "1px solid #ddd",
                display: "flex",
                alignItems: "center",
                gap: "10px"
            }}>
                <h1 style={{ margin: 0, color: "#333" }}>🌐 Memori Browser</h1>

                {/* URL Bar */}
                <form onSubmit={handleSubmit} style={{ flex: 1, maxWidth: "600px" }}>
                    <input
                        type="text"
                        value={url}
                        onChange={(e) => setUrl(e.target.value)}
                        placeholder="Enter URL or search..."
                        style={{
                            width: "100%",
                            padding: "12px",
                            fontSize: "16px",
                            border: "2px solid #ddd",
                            borderRadius: "8px",
                            outline: "none"
                        }}
                        autoFocus
                    />
                </form>

                <button
                    onClick={handleSubmit}
                    style={{
                        padding: "12px 24px",
                        backgroundColor: "#007bff",
                        color: "white",
                        border: "none",
                        borderRadius: "8px",
                        cursor: "pointer",
                        fontSize: "16px"
                    }}
                >
                    Go
                </button>
            </div>

            {/* Content */}
            <div style={{
                flex: 1,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "white",
                margin: "20px",
                borderRadius: "8px",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
            }}>
                <div style={{ textAlign: "center" }}>
                    <div style={{ fontSize: "48px", marginBottom: "20px" }}>🚀</div>
                    <h2 style={{ color: "#333", marginBottom: "10px" }}>Welcome to Memori Browser!</h2>
                    <p style={{ color: "#666", marginBottom: "30px" }}>
                        AI-Enhanced Browsing Experience
                    </p>
                    <div style={{ display: "flex", gap: "10px", justifyContent: "center" }}>
                        <button
                            onClick={() => setUrl("https://google.com")}
                            style={{
                                padding: "10px 20px",
                                backgroundColor: "#4285f4",
                                color: "white",
                                border: "none",
                                borderRadius: "6px",
                                cursor: "pointer"
                            }}
                        >
                            Visit Google
                        </button>
                        <button
                            onClick={() => setUrl("https://github.com")}
                            style={{
                                padding: "10px 20px",
                                backgroundColor: "#333",
                                color: "white",
                                border: "none",
                                borderRadius: "6px",
                                cursor: "pointer"
                            }}
                        >
                            Visit GitHub
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default App;
