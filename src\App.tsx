﻿import React, { useState, useEffect, useRef } from "react";
import { WebView } from "./components/WebView";

interface Tab {
    id: string;
    title: string;
    url: string;
    isLoading: boolean;
    favicon?: string;
}

function App() {
    const [tabs, setTabs] = useState<Tab[]>([
        {
            id: "tab-1",
            title: "New Tab",
            url: "about:blank",
            isLoading: false,
            favicon: "🌐"
        }
    ]);
    const [activeTabId, setActiveTabId] = useState("tab-1");
    const [url, setUrl] = useState("");
    const [history, setHistory] = useState<string[]>([]);
    const [historyIndex, setHistoryIndex] = useState(-1);
    const webviewRef = useRef<HTMLIFrameElement>(null);

    const activeTab = tabs.find(tab => tab.id === activeTabId);

    useEffect(() => {
        if (activeTab) {
            setUrl(activeTab.url === "about:blank" ? "" : activeTab.url);
        }
    }, [activeTab]);

    const updateTabTitle = (tabId: string, title: string) => {
        setTabs(prevTabs =>
            prevTabs.map(tab =>
                tab.id === tabId ? { ...tab, title } : tab
            )
        );
    };

    const navigateToUrl = (targetUrl: string) => {
        if (!targetUrl.trim()) return;

        // Handle search vs URL
        let finalUrl = targetUrl;
        if (!targetUrl.includes('.') || (!targetUrl.startsWith('http://') && !targetUrl.startsWith('https://'))) {
            // It's a search query
            finalUrl = `https://www.google.com/search?q=${encodeURIComponent(targetUrl)}`;
        } else if (!targetUrl.startsWith('http://') && !targetUrl.startsWith('https://')) {
            finalUrl = 'https://' + targetUrl;
        }

        // Update active tab
        setTabs(prevTabs =>
            prevTabs.map(tab =>
                tab.id === activeTabId
                    ? { ...tab, url: finalUrl, isLoading: true, title: "Loading..." }
                    : tab
            )
        );

        // Update history
        setHistory(prev => [...prev, finalUrl]);
        setHistoryIndex(prev => prev + 1);

        // Simulate loading completion
        setTimeout(() => {
            setTabs(prevTabs =>
                prevTabs.map(tab =>
                    tab.id === activeTabId
                        ? { ...tab, isLoading: false, title: getDomainFromUrl(finalUrl) }
                        : tab
                )
            );
        }, 1000);
    };

    const getDomainFromUrl = (url: string): string => {
        try {
            return new URL(url).hostname.replace('www.', '');
        } catch {
            return url;
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        navigateToUrl(url);
    };

    const createNewTab = () => {
        const newTabId = `tab-${Date.now()}`;
        const newTab: Tab = {
            id: newTabId,
            title: "New Tab",
            url: "about:blank",
            isLoading: false,
            favicon: "🌐"
        };

        setTabs(prevTabs => [...prevTabs, newTab]);
        setActiveTabId(newTabId);
        setUrl("");
    };

    const closeTab = (tabId: string) => {
        if (tabs.length === 1) return;

        setTabs(prevTabs => prevTabs.filter(tab => tab.id !== tabId));

        if (tabId === activeTabId) {
            const remainingTabs = tabs.filter(tab => tab.id !== tabId);
            setActiveTabId(remainingTabs[0]?.id || "");
        }
    };

    const switchTab = (tabId: string) => {
        setActiveTabId(tabId);
    };

    const goBack = () => {
        if (historyIndex > 0) {
            setHistoryIndex(prev => prev - 1);
            const prevUrl = history[historyIndex - 1];
            setUrl(prevUrl);
            navigateToUrl(prevUrl);
        }
    };

    const goForward = () => {
        if (historyIndex < history.length - 1) {
            setHistoryIndex(prev => prev + 1);
            const nextUrl = history[historyIndex + 1];
            setUrl(nextUrl);
            navigateToUrl(nextUrl);
        }
    };

    const refresh = () => {
        if (activeTab && activeTab.url !== "about:blank") {
            navigateToUrl(activeTab.url);
        }
    };

    return (
        <div style={{
            height: "100vh",
            display: "flex",
            flexDirection: "column",
            fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
            backgroundColor: "#f8f9fa",
            overflow: "hidden"
        }}>
            {/* Tab Bar */}
            <div style={{
                backgroundColor: "#e9ecef",
                display: "flex",
                alignItems: "flex-end",
                paddingLeft: "8px",
                borderBottom: "1px solid #dee2e6",
                minHeight: "40px"
            }}>
                {tabs.map(tab => (
                    <div
                        key={tab.id}
                        onClick={() => switchTab(tab.id)}
                        style={{
                            padding: "8px 16px",
                            backgroundColor: tab.id === activeTabId ? "#ffffff" : "#e9ecef",
                            border: "1px solid #dee2e6",
                            borderBottom: tab.id === activeTabId ? "1px solid #ffffff" : "1px solid #dee2e6",
                            borderRadius: "8px 8px 0 0",
                            cursor: "pointer",
                            marginRight: "2px",
                            display: "flex",
                            alignItems: "center",
                            gap: "8px",
                            minWidth: "140px",
                            maxWidth: "240px",
                            position: "relative",
                            transition: "all 0.2s ease"
                        }}
                    >
                        <span style={{ fontSize: "14px" }}>
                            {tab.isLoading ? "⏳" : tab.favicon || "🌐"}
                        </span>
                        <span style={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            flex: 1,
                            fontSize: "13px",
                            color: "#495057"
                        }}>
                            {tab.title}
                        </span>
                        {tabs.length > 1 && (
                            <button
                                onClick={(e) => {
                                    e.stopPropagation();
                                    closeTab(tab.id);
                                }}
                                style={{
                                    background: "none",
                                    border: "none",
                                    cursor: "pointer",
                                    padding: "4px",
                                    borderRadius: "4px",
                                    fontSize: "12px",
                                    color: "#6c757d",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    width: "20px",
                                    height: "20px"
                                }}
                                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = "#f8f9fa"}
                                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = "transparent"}
                            >
                                ✕
                            </button>
                        )}
                    </div>
                ))}
                <button
                    onClick={createNewTab}
                    style={{
                        padding: "8px 12px",
                        backgroundColor: "transparent",
                        border: "none",
                        cursor: "pointer",
                        fontSize: "18px",
                        color: "#6c757d",
                        borderRadius: "4px",
                        transition: "all 0.2s ease"
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = "#f8f9fa"}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = "transparent"}
                >
                    +
                </button>
            </div>

            {/* Navigation Bar */}
            <div style={{
                backgroundColor: "#ffffff",
                padding: "8px 12px",
                borderBottom: "1px solid #dee2e6",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                boxShadow: "0 1px 3px rgba(0,0,0,0.1)"
            }}>
                {/* Navigation Controls */}
                <div style={{ display: "flex", gap: "4px" }}>
                    <button
                        onClick={goBack}
                        disabled={historyIndex <= 0}
                        style={{
                            padding: "8px 12px",
                            border: "1px solid #dee2e6",
                            borderRadius: "6px",
                            cursor: historyIndex <= 0 ? "not-allowed" : "pointer",
                            backgroundColor: "#f8f9fa",
                            color: historyIndex <= 0 ? "#adb5bd" : "#495057",
                            fontSize: "14px",
                            transition: "all 0.2s ease"
                        }}
                        onMouseEnter={(e) => {
                            if (historyIndex > 0) e.currentTarget.style.backgroundColor = "#e9ecef";
                        }}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = "#f8f9fa"}
                    >
                        ←
                    </button>
                    <button
                        onClick={goForward}
                        disabled={historyIndex >= history.length - 1}
                        style={{
                            padding: "8px 12px",
                            border: "1px solid #dee2e6",
                            borderRadius: "6px",
                            cursor: historyIndex >= history.length - 1 ? "not-allowed" : "pointer",
                            backgroundColor: "#f8f9fa",
                            color: historyIndex >= history.length - 1 ? "#adb5bd" : "#495057",
                            fontSize: "14px",
                            transition: "all 0.2s ease"
                        }}
                        onMouseEnter={(e) => {
                            if (historyIndex < history.length - 1) e.currentTarget.style.backgroundColor = "#e9ecef";
                        }}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = "#f8f9fa"}
                    >
                        →
                    </button>
                    <button
                        onClick={refresh}
                        style={{
                            padding: "8px 12px",
                            border: "1px solid #dee2e6",
                            borderRadius: "6px",
                            cursor: "pointer",
                            backgroundColor: "#f8f9fa",
                            color: "#495057",
                            fontSize: "14px",
                            transition: "all 0.2s ease"
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = "#e9ecef"}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = "#f8f9fa"}
                    >
                        ↻
                    </button>
                </div>

                {/* URL Bar */}
                <form onSubmit={handleSubmit} style={{ flex: 1, maxWidth: "800px" }}>
                    <div style={{ position: "relative" }}>
                        <input
                            type="text"
                            value={url}
                            onChange={(e) => setUrl(e.target.value)}
                            placeholder="Search Google or type a URL"
                            style={{
                                width: "100%",
                                padding: "12px 16px",
                                fontSize: "14px",
                                border: "2px solid #dee2e6",
                                borderRadius: "24px",
                                outline: "none",
                                backgroundColor: "#f8f9fa",
                                transition: "all 0.2s ease"
                            }}
                            onFocus={(e) => {
                                e.currentTarget.style.backgroundColor = "#ffffff";
                                e.currentTarget.style.borderColor = "#0d6efd";
                                e.currentTarget.style.boxShadow = "0 0 0 3px rgba(13, 110, 253, 0.1)";
                            }}
                            onBlur={(e) => {
                                e.currentTarget.style.backgroundColor = "#f8f9fa";
                                e.currentTarget.style.borderColor = "#dee2e6";
                                e.currentTarget.style.boxShadow = "none";
                            }}
                        />
                        {activeTab?.isLoading && (
                            <div style={{
                                position: "absolute",
                                right: "16px",
                                top: "50%",
                                transform: "translateY(-50%)",
                                fontSize: "16px"
                            }}>
                                ⏳
                            </div>
                        )}
                    </div>
                </form>

                {/* Browser Menu */}
                <button
                    style={{
                        padding: "8px 12px",
                        border: "1px solid #dee2e6",
                        borderRadius: "6px",
                        cursor: "pointer",
                        backgroundColor: "#f8f9fa",
                        color: "#495057",
                        fontSize: "14px",
                        transition: "all 0.2s ease"
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = "#e9ecef"}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = "#f8f9fa"}
                >
                    ⋮
                </button>
            </div>

            {/* Main Content Area */}
            <div style={{
                flex: 1,
                backgroundColor: "#ffffff",
                position: "relative",
                overflow: "hidden"
            }}>
                {activeTab?.url === "about:blank" ? (
                    /* New Tab Page */
                    <div style={{
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        backgroundColor: "#f8f9fa"
                    }}>
                        <div style={{
                            textAlign: "center",
                            maxWidth: "600px",
                            padding: "40px"
                        }}>
                            <div style={{
                                fontSize: "64px",
                                marginBottom: "24px",
                                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                                WebkitBackgroundClip: "text",
                                WebkitTextFillColor: "transparent",
                                backgroundClip: "text"
                            }}>
                                🚀
                            </div>
                            <h1 style={{
                                color: "#212529",
                                marginBottom: "16px",
                                fontSize: "32px",
                                fontWeight: "300"
                            }}>
                                Welcome to Memori Browser
                            </h1>
                            <p style={{
                                color: "#6c757d",
                                marginBottom: "40px",
                                fontSize: "18px",
                                lineHeight: "1.6"
                            }}>
                                The AI-enhanced browser that learns from your browsing patterns
                            </p>

                            {/* Quick Access Grid */}
                            <div style={{
                                display: "grid",
                                gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
                                gap: "16px",
                                marginBottom: "40px"
                            }}>
                                <button
                                    onClick={() => navigateToUrl("google.com")}
                                    style={{
                                        padding: "20px",
                                        backgroundColor: "#ffffff",
                                        border: "2px solid #e9ecef",
                                        borderRadius: "12px",
                                        cursor: "pointer",
                                        fontSize: "16px",
                                        fontWeight: "500",
                                        color: "#495057",
                                        transition: "all 0.3s ease",
                                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                                    }}
                                    onMouseEnter={(e) => {
                                        e.currentTarget.style.transform = "translateY(-2px)";
                                        e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
                                        e.currentTarget.style.borderColor = "#4285f4";
                                    }}
                                    onMouseLeave={(e) => {
                                        e.currentTarget.style.transform = "translateY(0)";
                                        e.currentTarget.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
                                        e.currentTarget.style.borderColor = "#e9ecef";
                                    }}
                                >
                                    <div style={{ fontSize: "24px", marginBottom: "8px" }}>🔍</div>
                                    Google Search
                                </button>

                                <button
                                    onClick={() => navigateToUrl("github.com")}
                                    style={{
                                        padding: "20px",
                                        backgroundColor: "#ffffff",
                                        border: "2px solid #e9ecef",
                                        borderRadius: "12px",
                                        cursor: "pointer",
                                        fontSize: "16px",
                                        fontWeight: "500",
                                        color: "#495057",
                                        transition: "all 0.3s ease",
                                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                                    }}
                                    onMouseEnter={(e) => {
                                        e.currentTarget.style.transform = "translateY(-2px)";
                                        e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
                                        e.currentTarget.style.borderColor = "#333";
                                    }}
                                    onMouseLeave={(e) => {
                                        e.currentTarget.style.transform = "translateY(0)";
                                        e.currentTarget.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
                                        e.currentTarget.style.borderColor = "#e9ecef";
                                    }}
                                >
                                    <div style={{ fontSize: "24px", marginBottom: "8px" }}>💻</div>
                                    GitHub
                                </button>

                                <button
                                    onClick={() => navigateToUrl("youtube.com")}
                                    style={{
                                        padding: "20px",
                                        backgroundColor: "#ffffff",
                                        border: "2px solid #e9ecef",
                                        borderRadius: "12px",
                                        cursor: "pointer",
                                        fontSize: "16px",
                                        fontWeight: "500",
                                        color: "#495057",
                                        transition: "all 0.3s ease",
                                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                                    }}
                                    onMouseEnter={(e) => {
                                        e.currentTarget.style.transform = "translateY(-2px)";
                                        e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
                                        e.currentTarget.style.borderColor = "#ff0000";
                                    }}
                                    onMouseLeave={(e) => {
                                        e.currentTarget.style.transform = "translateY(0)";
                                        e.currentTarget.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
                                        e.currentTarget.style.borderColor = "#e9ecef";
                                    }}
                                >
                                    <div style={{ fontSize: "24px", marginBottom: "8px" }}>📺</div>
                                    YouTube
                                </button>

                                <button
                                    onClick={() => navigateToUrl("stackoverflow.com")}
                                    style={{
                                        padding: "20px",
                                        backgroundColor: "#ffffff",
                                        border: "2px solid #e9ecef",
                                        borderRadius: "12px",
                                        cursor: "pointer",
                                        fontSize: "16px",
                                        fontWeight: "500",
                                        color: "#495057",
                                        transition: "all 0.3s ease",
                                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                                    }}
                                    onMouseEnter={(e) => {
                                        e.currentTarget.style.transform = "translateY(-2px)";
                                        e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
                                        e.currentTarget.style.borderColor = "#f48024";
                                    }}
                                    onMouseLeave={(e) => {
                                        e.currentTarget.style.transform = "translateY(0)";
                                        e.currentTarget.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
                                        e.currentTarget.style.borderColor = "#e9ecef";
                                    }}
                                >
                                    <div style={{ fontSize: "24px", marginBottom: "8px" }}>📚</div>
                                    Stack Overflow
                                </button>
                            </div>

                            <p style={{
                                color: "#adb5bd",
                                fontSize: "14px",
                                fontStyle: "italic"
                            }}>
                                Start typing in the address bar to search or navigate to any website
                            </p>
                        </div>
                    </div>
                ) : (
                    /* Embedded Website Content */
                    <div style={{
                        width: "100%",
                        height: "100%",
                        position: "relative",
                        backgroundColor: "#ffffff"
                    }}>
                        {activeTab?.isLoading ? (
                            <div style={{
                                position: "absolute",
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                backgroundColor: "#f8f9fa",
                                zIndex: 1000
                            }}>
                                <div style={{ textAlign: "center" }}>
                                    <div style={{
                                        fontSize: "48px",
                                        marginBottom: "16px",
                                        animation: "spin 2s linear infinite"
                                    }}>
                                        ⏳
                                    </div>
                                    <p style={{ color: "#6c757d", fontSize: "18px" }}>
                                        Loading {getDomainFromUrl(activeTab.url)}...
                                    </p>
                                </div>
                            </div>
                        ) : null}

                        {/* Embedded Website Frame */}
                        <WebView
                            url={activeTab?.url || ""}
                            onLoad={() => {
                                setTabs(prevTabs =>
                                    prevTabs.map(tab =>
                                        tab.id === activeTabId
                                            ? { ...tab, isLoading: false }
                                            : tab
                                    )
                                );
                            }}
                            onError={() => {
                                setTabs(prevTabs =>
                                    prevTabs.map(tab =>
                                        tab.id === activeTabId
                                            ? { ...tab, isLoading: false, title: "Failed to load" }
                                            : tab
                                    )
                                );
                            }}
                            onTitleChange={(title) => {
                                setTabs(prevTabs =>
                                    prevTabs.map(tab =>
                                        tab.id === activeTabId
                                            ? { ...tab, title: title || getDomainFromUrl(tab.url) }
                                            : tab
                                    )
                                );
                            }}
                        />

                        {/* Website Controls Overlay */}
                        <div style={{
                            position: "absolute",
                            top: "10px",
                            right: "10px",
                            display: "flex",
                            gap: "8px",
                            zIndex: 100
                        }}>
                            <button
                                onClick={() => {
                                    if (webviewRef.current) {
                                        webviewRef.current.src = activeTab?.url || "";
                                    }
                                }}
                                style={{
                                    padding: "8px 12px",
                                    backgroundColor: "rgba(255, 255, 255, 0.9)",
                                    border: "1px solid #dee2e6",
                                    borderRadius: "6px",
                                    cursor: "pointer",
                                    fontSize: "12px",
                                    fontWeight: "500",
                                    color: "#495057",
                                    backdropFilter: "blur(10px)",
                                    boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
                                }}
                                title="Refresh page"
                            >
                                ↻
                            </button>
                            <button
                                onClick={() => {
                                    if (activeTab?.url) {
                                        window.open(activeTab.url, '_blank');
                                    }
                                }}
                                style={{
                                    padding: "8px 12px",
                                    backgroundColor: "rgba(255, 255, 255, 0.9)",
                                    border: "1px solid #dee2e6",
                                    borderRadius: "6px",
                                    cursor: "pointer",
                                    fontSize: "12px",
                                    fontWeight: "500",
                                    color: "#495057",
                                    backdropFilter: "blur(10px)",
                                    boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
                                }}
                                title="Open in system browser"
                            >
                                ↗
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

export default App;
