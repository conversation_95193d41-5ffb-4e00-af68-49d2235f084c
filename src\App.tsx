import React, { useState } from "react";

function App() {
    const [url, setUrl] = useState("https://google.com");

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Simple alert for now to test if <PERSON><PERSON> is working
        alert(`Navigating to: ${url}`);
        // Open in system browser
        window.open(url.startsWith('http') ? url : 'https://' + url, '_blank');
    };

    return (
        <div style={{
            height: "100vh",
            display: "flex",
            flexDirection: "column",
            fontFamily: "Arial, sans-serif",
            backgroundColor: "#f0f0f0"
        }}>
            {/* Tab Bar */}
            <div style={{
                backgroundColor: "#e0e0e0",
                display: "flex",
                alignItems: "flex-end",
                paddingLeft: "10px",
                borderBottom: "1px solid #ccc"
            }}>
                {tabs.map(tab => (
                    <div
                        key={tab.id}
                        onClick={() => switchTab(tab.id)}
                        style={{
                            padding: "8px 16px",
                            backgroundColor: tab.id === activeTabId ? "white" : "#d0d0d0",
                            border: "1px solid #ccc",
                            borderBottom: tab.id === activeTabId ? "1px solid white" : "1px solid #ccc",
                            borderRadius: "8px 8px 0 0",
                            cursor: "pointer",
                            marginRight: "2px",
                            display: "flex",
                            alignItems: "center",
                            gap: "8px",
                            minWidth: "120px",
                            maxWidth: "200px"
                        }}
                    >
                        <span style={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            flex: 1,
                            fontSize: "14px"
                        }}>
                            {tab.isLoading ? "⏳" : "🌐"} {tab.title}
                        </span>
                        {tabs.length > 1 && (
                            <button
                                onClick={(e) => {
                                    e.stopPropagation();
                                    closeTab(tab.id);
                                }}
                                style={{
                                    background: "none",
                                    border: "none",
                                    cursor: "pointer",
                                    padding: "2px",
                                    borderRadius: "3px",
                                    fontSize: "12px"
                                }}
                            >
                                ✕
                            </button>
                        )}
                    </div>
                ))}
                <button
                    onClick={createNewTab}
                    style={{
                        padding: "8px 12px",
                        backgroundColor: "#d0d0d0",
                        border: "1px solid #ccc",
                        borderRadius: "8px 8px 0 0",
                        cursor: "pointer",
                        fontSize: "16px"
                    }}
                >
                    +
                </button>
            </div>

            {/* Navigation Bar */}
            <div style={{
                backgroundColor: "white",
                padding: "10px",
                borderBottom: "1px solid #ddd",
                display: "flex",
                alignItems: "center",
                gap: "10px"
            }}>
                <div style={{ display: "flex", gap: "5px" }}>
                    <button style={{ padding: "8px", border: "1px solid #ddd", borderRadius: "4px", cursor: "pointer" }}>⬅️</button>
                    <button style={{ padding: "8px", border: "1px solid #ddd", borderRadius: "4px", cursor: "pointer" }}>➡️</button>
                    <button style={{ padding: "8px", border: "1px solid #ddd", borderRadius: "4px", cursor: "pointer" }}>🔄</button>
                </div>

                <h1 style={{ margin: 0, color: "#333", fontSize: "18px" }}>🌐 Memori</h1>

                {/* URL Bar */}
                <form onSubmit={handleSubmit} style={{ flex: 1, maxWidth: "600px" }}>
                    <input
                        type="text"
                        value={url}
                        onChange={(e) => setUrl(e.target.value)}
                        placeholder="Enter URL or search..."
                        style={{
                            width: "100%",
                            padding: "12px",
                            fontSize: "16px",
                            border: "2px solid #ddd",
                            borderRadius: "8px",
                            outline: "none"
                        }}
                    />
                </form>

                <button
                    onClick={handleSubmit}
                    style={{
                        padding: "12px 24px",
                        backgroundColor: "#007bff",
                        color: "white",
                        border: "none",
                        borderRadius: "8px",
                        cursor: "pointer",
                        fontSize: "16px"
                    }}
                >
                    Go
                </button>
            </div>

            {/* Content */}
            <div style={{
                flex: 1,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "white",
                margin: "20px",
                borderRadius: "8px",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
            }}>
                {activeTab?.url === "about:blank" ? (
                    <div style={{ textAlign: "center" }}>
                        <div style={{ fontSize: "48px", marginBottom: "20px" }}>🚀</div>
                        <h2 style={{ color: "#333", marginBottom: "10px" }}>Welcome to Memori Browser!</h2>
                        <p style={{ color: "#666", marginBottom: "30px" }}>
                            AI-Enhanced Browsing Experience - Enter a URL above to get started
                        </p>
                        <div style={{ display: "flex", gap: "10px", justifyContent: "center", flexWrap: "wrap" }}>
                            <button
                                onClick={() => navigateToUrl("https://google.com")}
                                style={{
                                    padding: "12px 20px",
                                    backgroundColor: "#4285f4",
                                    color: "white",
                                    border: "none",
                                    borderRadius: "6px",
                                    cursor: "pointer",
                                    fontSize: "16px"
                                }}
                            >
                                🔍 Google
                            </button>
                            <button
                                onClick={() => navigateToUrl("https://github.com")}
                                style={{
                                    padding: "12px 20px",
                                    backgroundColor: "#333",
                                    color: "white",
                                    border: "none",
                                    borderRadius: "6px",
                                    cursor: "pointer",
                                    fontSize: "16px"
                                }}
                            >
                                💻 GitHub
                            </button>
                            <button
                                onClick={() => navigateToUrl("https://stackoverflow.com")}
                                style={{
                                    padding: "12px 20px",
                                    backgroundColor: "#f48024",
                                    color: "white",
                                    border: "none",
                                    borderRadius: "6px",
                                    cursor: "pointer",
                                    fontSize: "16px"
                                }}
                            >
                                📚 Stack Overflow
                            </button>
                            <button
                                onClick={() => navigateToUrl("https://youtube.com")}
                                style={{
                                    padding: "12px 20px",
                                    backgroundColor: "#ff0000",
                                    color: "white",
                                    border: "none",
                                    borderRadius: "6px",
                                    cursor: "pointer",
                                    fontSize: "16px"
                                }}
                            >
                                📺 YouTube
                            </button>
                        </div>
                    </div>
                ) : (
                    <div style={{
                        width: "100%",
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        flexDirection: "column",
                        gap: "20px"
                    }}>
                        {activeTab?.isLoading ? (
                            <>
                                <div style={{ fontSize: "32px" }}>⏳</div>
                                <p>Loading {activeTab.url}...</p>
                            </>
                        ) : (
                            <>
                                <div style={{ fontSize: "32px" }}>🌐</div>
                                <p>Opened {activeTab?.url} in your system browser</p>
                                <p style={{ color: "#666", fontSize: "14px" }}>
                                    (Future versions will display websites directly in this window)
                                </p>
                            </>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}

export default App;
